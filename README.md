# Spring WebFlux MCP 服务器代理

一个基于 Spring WebFlux 的代理服务器，将基于 stdio 的 MCP（模型上下文协议）服务器转换为基于 HTTP 的 MCP 服务器，遵循 [open-webui/mcpo](https://github.com/open-webui/mcpo) 的架构模式。

## 概述

本实现提供了一个全面的解决方案，用于在沙箱环境中运行 MCP 服务器并通过 HTTP API 公开它们。包括：

- **MCP 协议实现**: 完整的基于 JSON-RPC 2.0 的 MCP 协议支持
- **沙箱执行**: 基于 Docker 的沙箱，具有可扩展架构
- **配置管理**: 用于管理 MCP 服务器配置的 Web 界面
- **HTTP 代理**: 将 HTTP 请求转换为 MCP 协议消息
- **资源监控**: 沙箱资源使用的实时监控

## 架构

### 核心组件

1. **MCP 协议层** (`com.example.springvueapp.mcp`)
   - JSON-RPC 2.0 消息处理
   - MCP 特定协议模型
   - stdio 通信的响应式客户端

2. **沙箱抽象** (`com.example.springvueapp.sandbox`)
   - `SandboxEnvironment`: 不同部署环境的抽象接口
   - `DockerSandboxEnvironment`: 基于 Docker 的实现
   - 为未来平台（Kubernetes、云函数等）的可扩展设计

3. **配置管理** (`com.example.springvueapp.service`)
   - MCP 服务器配置的 CRUD 操作
   - 验证和持久化
   - 用户范围的访问控制

4. **HTTP 代理服务** (`com.example.springvueapp.service.McpProxyService`)
   - 管理沙箱生命周期
   - 将 HTTP 请求路由到 MCP 服务器
   - 处理会话状态和错误恢复

### 前端组件

1. **Vue.js 管理界面**
   - 配置管理 UI
   - 实时服务器监控
   - 工具测试界面
   - 资源使用可视化

## 主要功能

### 1. 沙箱执行环境

- **Docker 集成**: 使用 Docker Java 客户端进行容器管理
- **资源限制**: 可配置的 CPU、内存和磁盘限制
- **网络隔离**: 具有可配置访问的安全网络
- **卷挂载**: 支持持久数据和代码注入

### 2. MCP 协议支持

- **完整 MCP 兼容性**: 实现 MCP 规范 2024-11-05
- **工具发现**: 自动发现可用工具
- **工具执行**: 具有适当错误处理的响应式工具调用
- **服务器信息**: 访问服务器元数据和功能

### 3. 配置管理

- **Web 界面**: 用户友好的配置管理
- **验证**: 配置的全面验证
- **模板**: 常见运行时的预配置模板
- **版本控制**: 跟踪配置更改

### 4. HTTP-to-MCP 代理

- **RESTful API**: 所有 MCP 操作的标准 HTTP 端点
- **请求映射**: 自动将 HTTP 请求转换为 MCP 调用
- **错误处理**: 适当的 HTTP 状态码和错误响应
- **身份验证**: 与现有 Spring Security 集成

## API 端点

### 配置管理

```
GET    /api/mcp/configurations           # 列出用户配置
POST   /api/mcp/configurations           # 创建新配置
GET    /api/mcp/configurations/{id}      # 获取特定配置
PUT    /api/mcp/configurations/{id}      # 更新配置
DELETE /api/mcp/configurations/{id}      # 删除配置
POST   /api/mcp/configurations/{id}/toggle # 切换启用状态
```

### 服务器管理

```
POST   /api/mcp/proxy/start/{configId}   # 启动 MCP 服务器实例
POST   /api/mcp/proxy/stop/{sandboxId}   # 停止 MCP 服务器实例
GET    /api/mcp/proxy/instances          # 列出运行中的实例
GET    /api/mcp/proxy/instances/{id}/status    # 获取实例状态
GET    /api/mcp/proxy/instances/{id}/resources # 获取资源使用情况
```

### MCP 操作

```
GET    /api/mcp/proxy/{sandboxId}/tools         # 列出可用工具
GET    /api/mcp/proxy/{sandboxId}/info          # 获取服务器信息
POST   /api/mcp/proxy/{sandboxId}/tools/{tool}  # 调用特定工具
POST   /api/mcp/proxy/{sandboxId}/http/{tool}   # HTTP-to-MCP 代理
```

## 配置示例

### 基本 Node.js MCP 服务器

```json
{
  "name": "time-server",
  "description": "用于时间操作的 MCP 服务器",
  "command": "node",
  "arguments": ["server.js"],
  "dockerImage": "node:18-alpine",
  "workingDirectory": "/app",
  "environment": {
    "NODE_ENV": "production",
    "TZ": "Asia/Shanghai"
  },
  "resourceLimits": {
    "memoryLimitBytes": 536870912,
    "cpuLimit": 1.0
  },
  "timeoutSeconds": 300,
  "autoRestart": true
}
```

### 带卷挂载的 Python MCP 服务器

```json
{
  "name": "python-tools",
  "description": "基于 Python 的 MCP 工具",
  "command": "python",
  "arguments": ["-m", "mcp_server"],
  "dockerImage": "python:3.11-alpine",
  "workingDirectory": "/app",
  "volumeMounts": [
    {
      "hostPath": "/host/data",
      "containerPath": "/app/data",
      "readOnly": false
    }
  ],
  "resourceLimits": {
    "memoryLimitBytes": 1073741824,
    "cpuLimit": 2.0
  }
}
```

## 安全考虑

### 沙箱安全

- **容器隔离**: 每个 MCP 服务器在自己的 Docker 容器中运行
- **资源限制**: 防止资源耗尽攻击
- **网络隔离**: 默认网络隔离，可配置访问
- **用户权限**: 容器内非 root 执行

### 访问控制

- **用户范围**: 配置和实例限定在已认证用户范围内
- **身份验证**: 与 Spring Security 集成
- **授权**: 管理功能的基于角色的访问控制

### 数据保护

- **输入验证**: 所有输入的全面验证
- **错误处理**: 安全的错误消息，不泄露信息
- **审计日志**: 所有操作的完整审计跟踪

## 部署

### 前提条件

- Java 17+
- Docker Engine
- Node.js 18+ (用于前端)
- Maven 3.6+

### 后端设置

```bash
cd backend
mvn clean install
mvn spring-boot:run
```

### 前端设置

```bash
cd frontend
npm install
npm run dev
```

### Docker 部署

```bash
# 构建后端
cd backend
mvn clean package
docker build -t mcp-proxy-backend .

# 构建前端
cd frontend
npm run build
docker build -t mcp-proxy-frontend .

# 使用 docker-compose 运行
docker-compose up -d
```

## 扩展点

### 添加新的沙箱环境

1. 实现 `SandboxEnvironment` 接口
2. 创建相应的 `SandboxInstance` 实现
3. 在 `McpConfig` 中添加配置 bean
4. 更新应用程序属性

Kubernetes 示例：

```java
@Component
@ConditionalOnProperty(name = "mcp.sandbox.type", havingValue = "kubernetes")
public class KubernetesSandboxEnvironment implements SandboxEnvironment {
    // Kubernetes pods 的实现
}
```

### 自定义 MCP 协议扩展

1. 在 `com.example.springvueapp.mcp.model` 中扩展 MCP 模型类
2. 为新消息类型更新 `McpClient`
3. 添加相应的 REST 端点

## 监控和可观测性

### 指标

- 沙箱创建/销毁率
- 每个实例的资源使用情况
- 工具调用成功/失败率
- 响应时间

### 日志

- 带有关联 ID 的结构化日志
- 不同组件的单独日志级别
- Docker 容器日志集成

### 健康检查

- Docker 守护进程连接性
- 数据库连接性
- 单个沙箱健康状况

## 未来增强

1. **Kubernetes 支持**: 原生 Kubernetes pod 执行
2. **云函数**: 无服务器执行环境
3. **负载均衡**: 同一 MCP 服务器的多个实例
4. **缓存**: 昂贵操作的响应缓存
5. **指标仪表板**: 实时监控界面
6. **自动扩缩**: 基于负载的动态扩缩
7. **插件系统**: 可扩展的插件架构

## 贡献

1. Fork 仓库
2. 创建功能分支
3. 实现更改并编写测试
4. 提交 Pull Request

## 许可证

MIT 许可证 - 详情请参阅 LICENSE 文件
