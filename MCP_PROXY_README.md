# Spring WebFlux MCP Server Proxy

A Spring WebFlux-based proxy that converts stdio-based MCP (Model Context Protocol) servers to HTTP-based MCP servers, following the architecture pattern from [open-webui/mcpo](https://github.com/open-webui/mcpo).

## Overview

This implementation provides a comprehensive solution for running MCP servers in sandboxed environments and exposing them via HTTP APIs. It includes:

- **MCP Protocol Implementation**: Full JSON-RPC 2.0 based MCP protocol support
- **Sandboxed Execution**: Docker-based sandbox with extensible architecture
- **Configuration Management**: Web-based interface for managing MCP server configurations
- **HTTP Proxy**: Converts HTTP requests to MCP protocol messages
- **Resource Monitoring**: Real-time monitoring of sandbox resource usage

## Architecture

### Core Components

1. **MCP Protocol Layer** (`com.example.springvueapp.mcp`)
   - JSON-RPC 2.0 message handling
   - MCP-specific protocol models
   - Reactive client for stdio communication

2. **Sandbox Abstraction** (`com.example.springvueapp.sandbox`)
   - `SandboxEnvironment`: Abstract interface for different deployment environments
   - `DockerSandboxEnvironment`: Docker-based implementation
   - Extensible design for future platforms (Kubernetes, cloud functions, etc.)

3. **Configuration Management** (`com.example.springvueapp.service`)
   - CRUD operations for MCP server configurations
   - Validation and persistence
   - User-scoped access control

4. **HTTP Proxy Service** (`com.example.springvueapp.service.McpProxyService`)
   - Manages sandbox lifecycle
   - Routes HTTP requests to MCP servers
   - Handles session state and error recovery

### Frontend Components

1. **Vue.js Management Interface**
   - Configuration management UI
   - Real-time server monitoring
   - Tool testing interface
   - Resource usage visualization

## Key Features

### 1. Sandboxed Execution Environment

- **Docker Integration**: Uses Docker Java client for container management
- **Resource Limits**: Configurable CPU, memory, and disk limits
- **Network Isolation**: Secure networking with configurable access
- **Volume Mounting**: Support for persistent data and code injection

### 2. MCP Protocol Support

- **Full MCP Compatibility**: Implements MCP specification 2024-11-05
- **Tool Discovery**: Automatic discovery of available tools
- **Tool Execution**: Reactive tool calling with proper error handling
- **Server Information**: Access to server metadata and capabilities

### 3. Configuration Management

- **Web Interface**: User-friendly configuration management
- **Validation**: Comprehensive validation of configurations
- **Templates**: Pre-configured templates for common runtimes
- **Version Control**: Track configuration changes over time

### 4. HTTP-to-MCP Proxy

- **RESTful API**: Standard HTTP endpoints for all MCP operations
- **Request Mapping**: Automatic conversion of HTTP requests to MCP calls
- **Error Handling**: Proper HTTP status codes and error responses
- **Authentication**: Integrated with existing Spring Security

## API Endpoints

### Configuration Management

```
GET    /api/mcp/configurations           # List user configurations
POST   /api/mcp/configurations           # Create new configuration
GET    /api/mcp/configurations/{id}      # Get specific configuration
PUT    /api/mcp/configurations/{id}      # Update configuration
DELETE /api/mcp/configurations/{id}      # Delete configuration
POST   /api/mcp/configurations/{id}/toggle # Toggle enabled status
```

### Server Management

```
POST   /api/mcp/proxy/start/{configId}   # Start MCP server instance
POST   /api/mcp/proxy/stop/{sandboxId}   # Stop MCP server instance
GET    /api/mcp/proxy/instances          # List running instances
GET    /api/mcp/proxy/instances/{id}/status    # Get instance status
GET    /api/mcp/proxy/instances/{id}/resources # Get resource usage
```

### MCP Operations

```
GET    /api/mcp/proxy/{sandboxId}/tools         # List available tools
GET    /api/mcp/proxy/{sandboxId}/info          # Get server information
POST   /api/mcp/proxy/{sandboxId}/tools/{tool}  # Call specific tool
POST   /api/mcp/proxy/{sandboxId}/http/{tool}   # HTTP-to-MCP proxy
```

## Configuration Examples

### Basic Node.js MCP Server

```json
{
  "name": "time-server",
  "description": "MCP server for time operations",
  "command": "node",
  "arguments": ["server.js"],
  "dockerImage": "node:18-alpine",
  "workingDirectory": "/app",
  "environment": {
    "NODE_ENV": "production",
    "TZ": "America/New_York"
  },
  "resourceLimits": {
    "memoryLimitBytes": 536870912,
    "cpuLimit": 1.0
  },
  "timeoutSeconds": 300,
  "autoRestart": true
}
```

### Python MCP Server with Volume Mount

```json
{
  "name": "python-tools",
  "description": "Python-based MCP tools",
  "command": "python",
  "arguments": ["-m", "mcp_server"],
  "dockerImage": "python:3.11-alpine",
  "workingDirectory": "/app",
  "volumeMounts": [
    {
      "hostPath": "/host/data",
      "containerPath": "/app/data",
      "readOnly": false
    }
  ],
  "resourceLimits": {
    "memoryLimitBytes": 1073741824,
    "cpuLimit": 2.0
  }
}
```

## Security Considerations

### Sandbox Security

- **Container Isolation**: Each MCP server runs in its own Docker container
- **Resource Limits**: Prevents resource exhaustion attacks
- **Network Isolation**: Default network isolation with configurable access
- **User Permissions**: Non-root execution within containers

### Access Control

- **User Scoping**: Configurations and instances are scoped to authenticated users
- **Authentication**: Integrated with Spring Security
- **Authorization**: Role-based access control for administrative functions

### Data Protection

- **Input Validation**: Comprehensive validation of all inputs
- **Error Handling**: Secure error messages without information leakage
- **Audit Logging**: Complete audit trail of all operations

## Deployment

### Prerequisites

- Java 17+
- Docker Engine
- Node.js 18+ (for frontend)
- Maven 3.6+

### Backend Setup

```bash
cd backend
mvn clean install
mvn spring-boot:run
```

### Frontend Setup

```bash
cd frontend
npm install
npm run dev
```

### Docker Deployment

```bash
# Build backend
cd backend
mvn clean package
docker build -t mcp-proxy-backend .

# Build frontend
cd frontend
npm run build
docker build -t mcp-proxy-frontend .

# Run with docker-compose
docker-compose up -d
```

## Extension Points

### Adding New Sandbox Environments

1. Implement `SandboxEnvironment` interface
2. Create corresponding `SandboxInstance` implementation
3. Add configuration bean in `McpConfig`
4. Update application properties

Example for Kubernetes:

```java
@Component
@ConditionalOnProperty(name = "mcp.sandbox.type", havingValue = "kubernetes")
public class KubernetesSandboxEnvironment implements SandboxEnvironment {
    // Implementation for Kubernetes pods
}
```

### Custom MCP Protocol Extensions

1. Extend MCP model classes in `com.example.springvueapp.mcp.model`
2. Update `McpClient` for new message types
3. Add corresponding REST endpoints

## Monitoring and Observability

### Metrics

- Sandbox creation/destruction rates
- Resource usage per instance
- Tool call success/failure rates
- Response times

### Logging

- Structured logging with correlation IDs
- Separate log levels for different components
- Docker container logs integration

### Health Checks

- Docker daemon connectivity
- Database connectivity
- Individual sandbox health

## Future Enhancements

1. **Kubernetes Support**: Native Kubernetes pod execution
2. **Cloud Functions**: Serverless execution environments
3. **Load Balancing**: Multiple instances of the same MCP server
4. **Caching**: Response caching for expensive operations
5. **Metrics Dashboard**: Real-time monitoring interface
6. **Auto-scaling**: Dynamic scaling based on load
7. **Plugin System**: Extensible plugin architecture

## Contributing

1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Submit a pull request

## License

MIT License - see LICENSE file for details.
