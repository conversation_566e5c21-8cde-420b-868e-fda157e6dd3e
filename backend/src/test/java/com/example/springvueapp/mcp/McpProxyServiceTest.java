package com.example.springvueapp.mcp;

import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.repository.McpServerInstanceRepository;
import com.example.springvueapp.sandbox.SandboxEnvironment;
import com.example.springvueapp.sandbox.SandboxInstance;
import com.example.springvueapp.sandbox.SandboxConfig;
import com.example.springvueapp.sandbox.SandboxStatus;
import com.example.springvueapp.service.McpProxyService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class McpProxyServiceTest {
    
    @Mock
    private SandboxEnvironment sandboxEnvironment;
    
    @Mock
    private McpServerInstanceRepository instanceRepository;
    
    @Mock
    private SandboxInstance sandboxInstance;
    
    private McpProxyService mcpProxyService;
    
    @BeforeEach
    void setUp() {
        mcpProxyService = new McpProxyService(sandboxEnvironment, instanceRepository);
    }
    
    @Test
    void testStartServer() {
        // Given
        McpServerConfiguration config = McpServerConfiguration.builder()
                .id(1L)
                .name("test-server")
                .command("node")
                .dockerImage("node:18-alpine")
                .build();
        
        Long userId = 1L;
        String sandboxId = "test-sandbox-123";
        
        when(sandboxEnvironment.createSandbox(any(SandboxConfig.class)))
                .thenReturn(Mono.just(sandboxInstance));
        when(sandboxInstance.getId()).thenReturn(sandboxId);
        when(sandboxInstance.start()).thenReturn(Mono.empty());
        when(sandboxEnvironment.getType()).thenReturn("docker");
        when(instanceRepository.save(any())).thenAnswer(invocation -> Mono.just(invocation.getArgument(0)));
        
        // When & Then
        StepVerifier.create(mcpProxyService.startServer(config, userId))
                .expectNextMatches(instance -> 
                        instance.getSandboxId().equals(sandboxId) &&
                        instance.getConfigurationId().equals(config.getId()) &&
                        instance.getUserId().equals(userId) &&
                        instance.getStatus() == SandboxStatus.RUNNING
                )
                .verifyComplete();
        
        verify(sandboxEnvironment).createSandbox(any(SandboxConfig.class));
        verify(sandboxInstance).start();
        verify(instanceRepository, times(2)).save(any());
    }
    
    @Test
    void testStopServer() {
        // Given
        String sandboxId = "test-sandbox-123";
        
        when(instanceRepository.findBySandboxId(sandboxId))
                .thenReturn(Mono.just(createTestInstance(sandboxId)));
        when(sandboxEnvironment.getSandbox(sandboxId))
                .thenReturn(Mono.just(sandboxInstance));
        when(sandboxInstance.stop()).thenReturn(Mono.empty());
        when(sandboxEnvironment.destroySandbox(sandboxId)).thenReturn(Mono.empty());
        when(instanceRepository.save(any())).thenAnswer(invocation -> Mono.just(invocation.getArgument(0)));
        
        // When & Then
        StepVerifier.create(mcpProxyService.stopServer(sandboxId))
                .verifyComplete();
        
        verify(sandboxInstance).stop();
        verify(sandboxEnvironment).destroySandbox(sandboxId);
        verify(instanceRepository).save(any());
    }
    
    private com.example.springvueapp.model.McpServerInstance createTestInstance(String sandboxId) {
        return com.example.springvueapp.model.McpServerInstance.builder()
                .id(1L)
                .configurationId(1L)
                .sandboxId(sandboxId)
                .status(SandboxStatus.RUNNING)
                .sandboxType("docker")
                .userId(1L)
                .startedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .build();
    }
}
