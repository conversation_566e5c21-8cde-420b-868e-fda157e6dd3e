# 服务器配置
server.port=8080

# R2DBC 数据库配置（响应式）
spring.r2dbc.url=r2dbc:h2:mem:///testdb
spring.r2dbc.username=sa
spring.r2dbc.password=password

# JDBC 数据库配置（传统）
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# H2 控制台配置
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# 日志配置
logging.level.org.springframework.data.r2dbc=DEBUG
logging.level.org.springframework.jdbc.core=DEBUG

# JWT 配置
jwt.secret=yourSecretKeyHereMakeItLongAndSecureForProductionUse
jwt.expiration=86400000


