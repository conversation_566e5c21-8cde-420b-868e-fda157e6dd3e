package com.example.springvueapp.sandbox;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;
import java.util.Map;

/**
 * Configuration for creating a sandbox instance
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SandboxConfig {
    
    /**
     * Unique identifier for this configuration
     */
    private String id;
    
    /**
     * Human-readable name for the sandbox
     */
    private String name;
    
    /**
     * Description of what this sandbox does
     */
    private String description;
    
    /**
     * Docker image to use (for Docker sandbox)
     */
    private String dockerImage;
    
    /**
     * Command to execute in the sandbox
     */
    private String command;
    
    /**
     * Arguments for the command
     */
    private List<String> arguments;
    
    /**
     * Environment variables
     */
    private Map<String, String> environment;
    
    /**
     * Working directory inside the sandbox
     */
    private String workingDirectory;
    
    /**
     * Resource limits
     */
    private ResourceLimits resourceLimits;
    
    /**
     * Network configuration
     */
    private NetworkConfig networkConfig;
    
    /**
     * Volume mounts
     */
    private List<VolumeMount> volumeMounts;
    
    /**
     * Timeout for operations (in seconds)
     */
    private Integer timeoutSeconds;
    
    /**
     * Whether to automatically restart on failure
     */
    private Boolean autoRestart;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ResourceLimits {
        private Long memoryLimitBytes;
        private Double cpuLimit;
        private Long diskLimitBytes;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class NetworkConfig {
        private Boolean enableNetworking;
        private List<String> allowedHosts;
        private List<Integer> exposedPorts;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class VolumeMount {
        private String hostPath;
        private String containerPath;
        private Boolean readOnly;
    }
}
