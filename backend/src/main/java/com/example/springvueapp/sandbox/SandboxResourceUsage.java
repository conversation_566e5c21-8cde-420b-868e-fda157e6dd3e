package com.example.springvueapp.sandbox;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * Resource usage statistics for a sandbox instance
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SandboxResourceUsage {
    
    /**
     * Timestamp when these metrics were collected
     */
    private LocalDateTime timestamp;
    
    /**
     * CPU usage percentage (0.0 to 100.0)
     */
    private Double cpuUsagePercent;
    
    /**
     * Memory usage in bytes
     */
    private Long memoryUsageBytes;
    
    /**
     * Memory limit in bytes
     */
    private Long memoryLimitBytes;
    
    /**
     * Memory usage percentage (0.0 to 100.0)
     */
    private Double memoryUsagePercent;
    
    /**
     * Disk usage in bytes
     */
    private Long diskUsageBytes;
    
    /**
     * Network bytes received
     */
    private Long networkBytesReceived;
    
    /**
     * Network bytes transmitted
     */
    private Long networkBytesTransmitted;
    
    /**
     * Process ID of the main process
     */
    private Long processId;
    
    /**
     * Number of running processes
     */
    private Integer processCount;
    
    /**
     * Uptime in seconds
     */
    private Long uptimeSeconds;
}
