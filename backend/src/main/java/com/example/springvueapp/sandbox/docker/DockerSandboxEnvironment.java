package com.example.springvueapp.sandbox.docker;

import com.example.springvueapp.sandbox.*;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.command.CreateContainerResponse;
import com.github.dockerjava.api.model.*;
import com.github.dockerjava.core.DefaultDockerClientConfig;
import com.github.dockerjava.core.DockerClientBuilder;
import com.github.dockerjava.httpclient5.ApacheDockerHttpClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Docker-based sandbox environment implementation
 */
@Slf4j
@Component
public class DockerSandboxEnvironment implements SandboxEnvironment {

    private DockerClient dockerClient;
    private final Map<String, DockerSandboxInstance> sandboxes = new ConcurrentHashMap<>();

    @PostConstruct
    public void initialize() {
        try {
            DefaultDockerClientConfig config = DefaultDockerClientConfig.createDefaultConfigBuilder()
                    .withDockerHost("unix:///var/run/docker.sock")
                    .build();

            ApacheDockerHttpClient httpClient = new ApacheDockerHttpClient.Builder()
                    .dockerHost(config.getDockerHost())
                    .sslConfig(config.getSSLConfig())
                    .maxConnections(100)
                    .connectionTimeout(Duration.ofSeconds(30))
                    .responseTimeout(Duration.ofSeconds(45))
                    .build();

            dockerClient = DockerClientBuilder.getInstance(config)
                    .withDockerHttpClient(httpClient)
                    .build();

            log.info("Docker client initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize Docker client", e);
            throw new RuntimeException("Docker client initialization failed", e);
        }
    }

    @PreDestroy
    public void shutdown() {
        cleanup().block();
        if (dockerClient != null) {
            try {
                dockerClient.close();
            } catch (Exception e) {
                log.error("Error closing Docker client", e);
            }
        }
    }

    @Override
    public Mono<SandboxInstance> createSandbox(SandboxConfig config) {
        return Mono.fromCallable(() -> {
            log.info("Creating Docker sandbox with config: {}", config.getName());

            // Build container creation command
            CreateContainerResponse container = createContainer(config);

            DockerSandboxInstance instance = new DockerSandboxInstance(
                    container.getId(),
                    config,
                    dockerClient
            );

            sandboxes.put(instance.getId(), instance);
            log.info("Created Docker sandbox: {}", instance.getId());

            return instance;
        })
        .onErrorMap(e -> new RuntimeException("Failed to create Docker sandbox", e));
    }

    private CreateContainerResponse createContainer(SandboxConfig config) {
        List<String> cmd = new ArrayList<>();
        cmd.add(config.getCommand());
        if (config.getArguments() != null) {
            cmd.addAll(config.getArguments());
        }

        HostConfig hostConfig = HostConfig.newHostConfig()
                .withAutoRemove(true)
                .withNetworkMode("none"); // Isolated by default

        // Set resource limits
        if (config.getResourceLimits() != null) {
            if (config.getResourceLimits().getMemoryLimitBytes() != null) {
                hostConfig.withMemory(config.getResourceLimits().getMemoryLimitBytes());
            }
            if (config.getResourceLimits().getCpuLimit() != null) {
                hostConfig.withCpuQuota(config.getResourceLimits().getCpuLimit().longValue() * 100000);
                hostConfig.withCpuPeriod(100000L);
            }
        }

        // Configure volumes
        if (config.getVolumeMounts() != null) {
            List<Bind> binds = new ArrayList<>();
            for (SandboxConfig.VolumeMount mount : config.getVolumeMounts()) {
                AccessMode accessMode = mount.getReadOnly() != null && mount.getReadOnly()
                        ? AccessMode.ro : AccessMode.rw;
                binds.add(new Bind(mount.getHostPath(), new Volume(mount.getContainerPath()), accessMode));
            }
            hostConfig.withBinds(binds);
        }

        return dockerClient.createContainerCmd(config.getDockerImage())
                .withCmd(cmd)
                .withEnv(buildEnvironmentList(config.getEnvironment()))
                .withWorkingDir(config.getWorkingDirectory())
                .withHostConfig(hostConfig)
                .withStdinOpen(true)
                .withAttachStdin(true)
                .withAttachStdout(true)
                .withAttachStderr(true)
                .exec();
    }

    private List<String> buildEnvironmentList(Map<String, String> environment) {
        if (environment == null) {
            return new ArrayList<>();
        }

        List<String> envList = new ArrayList<>();
        environment.forEach((key, value) -> envList.add(key + "=" + value));
        return envList;
    }

    @Override
    public Mono<SandboxInstance> getSandbox(String sandboxId) {
        return Mono.fromCallable(() -> sandboxes.get(sandboxId));
    }

    @Override
    public Mono<Map<String, SandboxInstance>> listSandboxes() {
        return Mono.fromCallable(() -> Map.copyOf(sandboxes));
    }

    @Override
    public Mono<Void> destroySandbox(String sandboxId) {
        return Mono.fromRunnable(() -> {
            DockerSandboxInstance instance = sandboxes.remove(sandboxId);
            if (instance != null) {
                instance.destroy().block();
            }
        });
    }

    @Override
    public Mono<Void> cleanup() {
        return Mono.fromRunnable(() -> {
            log.info("Cleaning up {} Docker sandboxes", sandboxes.size());
            sandboxes.values().forEach(instance -> {
                try {
                    instance.destroy().block();
                } catch (Exception e) {
                    log.error("Error destroying sandbox: {}", instance.getId(), e);
                }
            });
            sandboxes.clear();
        });
    }

    @Override
    public String getType() {
        return "docker";
    }

    @Override
    public Mono<Boolean> isHealthy() {
        return Mono.fromCallable(() -> {
            try {
                dockerClient.pingCmd().exec();
                return true;
            } catch (Exception e) {
                log.error("Docker health check failed", e);
                return false;
            }
        });
    }
}
