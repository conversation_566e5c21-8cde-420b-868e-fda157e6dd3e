package com.example.springvueapp.sandbox.docker;

import com.example.springvueapp.sandbox.*;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.async.ResultCallback;
import com.github.dockerjava.api.command.AttachContainerCmd;
import com.github.dockerjava.api.model.Frame;
import com.github.dockerjava.api.model.Statistics;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.io.*;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Docker-based sandbox instance implementation
 */
@Slf4j
public class DockerSandboxInstance implements SandboxInstance {

    private final String containerId;
    private final SandboxConfig config;
    private final DockerClient dockerClient;
    private final LocalDateTime createdAt;
    private final AtomicReference<SandboxStatus> status;

    private PipedOutputStream stdinOutputStream;
    private PipedInputStream stdinInputStream;
    private final Sinks.Many<String> stdoutSink;
    private final Sinks.Many<String> stderrSink;

    public DockerSandboxInstance(String containerId, SandboxConfig config, DockerClient dockerClient) {
        this.containerId = containerId;
        this.config = config;
        this.dockerClient = dockerClient;
        this.createdAt = LocalDateTime.now();
        this.status = new AtomicReference<>(SandboxStatus.CREATED);
        this.stdoutSink = Sinks.many().multicast().onBackpressureBuffer();
        this.stderrSink = Sinks.many().multicast().onBackpressureBuffer();

        try {
            this.stdinOutputStream = new PipedOutputStream();
            this.stdinInputStream = new PipedInputStream(stdinOutputStream);
        } catch (IOException e) {
            throw new RuntimeException("Failed to create stdin pipes", e);
        }
    }

    @Override
    public String getId() {
        return containerId;
    }

    @Override
    public SandboxConfig getConfig() {
        return config;
    }

    @Override
    public SandboxStatus getStatus() {
        return status.get();
    }

    @Override
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Override
    public Mono<Void> start() {
        return Mono.fromRunnable(() -> {
            try {
                status.set(SandboxStatus.STARTING);
                log.info("Starting Docker container: {}", containerId);

                // Start the container
                dockerClient.startContainerCmd(containerId).exec();

                // Attach to container for I/O
                AttachContainerCmd attachCmd = dockerClient.attachContainerCmd(containerId)
                        .withStdIn(true)
                        .withStdOut(true)
                        .withStdErr(true)
                        .withFollowStream(true)
                        .withLogs(true);

                attachCmd.exec(new ResultCallback.Adapter<Frame>() {
                    @Override
                    public void onNext(Frame frame) {
                        String content = new String(frame.getPayload()).trim();
                        if (!content.isEmpty()) {
                            switch (frame.getStreamType()) {
                                case STDOUT:
                                    stdoutSink.tryEmitNext(content);
                                    break;
                                case STDERR:
                                    stderrSink.tryEmitNext(content);
                                    break;
                                default:
                                    break;
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable throwable) {
                        log.error("Error in container attachment: {}", containerId, throwable);
                        status.set(SandboxStatus.FAILED);
                        stdoutSink.tryEmitError(throwable);
                        stderrSink.tryEmitError(throwable);
                    }

                    @Override
                    public void onComplete() {
                        log.info("Container attachment completed: {}", containerId);
                        status.set(SandboxStatus.STOPPED);
                        stdoutSink.tryEmitComplete();
                        stderrSink.tryEmitComplete();
                    }
                });

                status.set(SandboxStatus.RUNNING);
                log.info("Docker container started successfully: {}", containerId);

            } catch (Exception e) {
                status.set(SandboxStatus.FAILED);
                log.error("Failed to start Docker container: {}", containerId, e);
                throw new RuntimeException("Failed to start container", e);
            }
        });
    }

    @Override
    public Mono<Void> stop() {
        return Mono.fromRunnable(() -> {
            try {
                status.set(SandboxStatus.STOPPING);
                log.info("Stopping Docker container: {}", containerId);

                dockerClient.stopContainerCmd(containerId)
                        .withTimeout(10)
                        .exec();

                status.set(SandboxStatus.STOPPED);
                log.info("Docker container stopped: {}", containerId);

            } catch (Exception e) {
                log.error("Failed to stop Docker container: {}", containerId, e);
                throw new RuntimeException("Failed to stop container", e);
            }
        });
    }

    @Override
    public Mono<Void> restart() {
        return stop().then(start());
    }

    @Override
    public OutputStream getStdinStream() {
        return stdinOutputStream;
    }

    @Override
    public InputStream getStdoutStream() {
        // This is a simplified implementation - in practice you'd want to buffer the output
        return new ByteArrayInputStream(new byte[0]);
    }

    @Override
    public InputStream getStderrStream() {
        // This is a simplified implementation - in practice you'd want to buffer the output
        return new ByteArrayInputStream(new byte[0]);
    }

    @Override
    public Flux<String> getStdoutFlux() {
        return stdoutSink.asFlux();
    }

    @Override
    public Flux<String> getStderrFlux() {
        return stderrSink.asFlux();
    }

    @Override
    public Mono<Void> writeToStdin(String data) {
        return Mono.fromRunnable(() -> {
            try {
                stdinOutputStream.write(data.getBytes());
                stdinOutputStream.flush();
            } catch (IOException e) {
                throw new RuntimeException("Failed to write to stdin", e);
            }
        });
    }

    @Override
    public boolean isRunning() {
        return status.get() == SandboxStatus.RUNNING;
    }

    @Override
    public Mono<SandboxResourceUsage> getResourceUsage() {
        return Mono.fromCallable(() -> {
            try {
                Statistics stats = dockerClient.statsCmd(containerId)
                        .withNoStream(true)
                        .exec();

                return SandboxResourceUsage.builder()
                        .timestamp(LocalDateTime.now())
                        .memoryUsageBytes(stats.getMemoryStats().getUsage())
                        .memoryLimitBytes(stats.getMemoryStats().getLimit())
                        .memoryUsagePercent(calculateMemoryUsagePercent(stats))
                        .cpuUsagePercent(calculateCpuUsagePercent(stats))
                        .networkBytesReceived(calculateNetworkBytesReceived(stats))
                        .networkBytesTransmitted(calculateNetworkBytesTransmitted(stats))
                        .uptimeSeconds(calculateUptimeSeconds())
                        .build();

            } catch (Exception e) {
                log.error("Failed to get resource usage for container: {}", containerId, e);
                return SandboxResourceUsage.builder()
                        .timestamp(LocalDateTime.now())
                        .build();
            }
        });
    }

    @Override
    public Mono<Void> destroy() {
        return Mono.fromRunnable(() -> {
            try {
                status.set(SandboxStatus.DESTROYING);
                log.info("Destroying Docker container: {}", containerId);

                // Stop container if running
                if (isRunning()) {
                    stop().block();
                }

                // Remove container
                dockerClient.removeContainerCmd(containerId)
                        .withForce(true)
                        .exec();

                status.set(SandboxStatus.DESTROYED);
                log.info("Docker container destroyed: {}", containerId);

                // Close streams
                try {
                    stdinOutputStream.close();
                    stdinInputStream.close();
                } catch (IOException e) {
                    log.warn("Error closing streams for container: {}", containerId, e);
                }

            } catch (Exception e) {
                log.error("Failed to destroy Docker container: {}", containerId, e);
                throw new RuntimeException("Failed to destroy container", e);
            }
        });
    }

    private Double calculateMemoryUsagePercent(Statistics stats) {
        if (stats.getMemoryStats().getUsage() != null && stats.getMemoryStats().getLimit() != null) {
            return (double) stats.getMemoryStats().getUsage() / stats.getMemoryStats().getLimit() * 100.0;
        }
        return null;
    }

    private Double calculateCpuUsagePercent(Statistics stats) {
        // Simplified CPU calculation - in practice this would be more complex
        if (stats.getCpuStats() != null && stats.getCpuStats().getCpuUsage() != null) {
            return stats.getCpuStats().getCpuUsage().getPercpuUsage().stream()
                    .mapToLong(Long::longValue)
                    .average()
                    .orElse(0.0);
        }
        return null;
    }

    private Long calculateNetworkBytesReceived(Statistics stats) {
        if (stats.getNetworks() != null) {
            return stats.getNetworks().values().stream()
                    .mapToLong(net -> net.getRxBytes() != null ? net.getRxBytes() : 0L)
                    .sum();
        }
        return null;
    }

    private Long calculateNetworkBytesTransmitted(Statistics stats) {
        if (stats.getNetworks() != null) {
            return stats.getNetworks().values().stream()
                    .mapToLong(net -> net.getTxBytes() != null ? net.getTxBytes() : 0L)
                    .sum();
        }
        return null;
    }

    private Long calculateUptimeSeconds() {
        return java.time.Duration.between(createdAt, LocalDateTime.now()).getSeconds();
    }
}
