package com.example.springvueapp.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;

/**
 * MCP Initialize response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class McpInitializeResponse {
    
    @JsonProperty("protocolVersion")
    private String protocolVersion;
    
    @JsonProperty("capabilities")
    private Map<String, Object> capabilities;
    
    @JsonProperty("serverInfo")
    private McpServerInfo serverInfo;
}
