package com.example.springvueapp.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * MCP Tool call response
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class McpToolCallResponse {
    
    @JsonProperty("content")
    private List<McpContent> content;
    
    @JsonProperty("isError")
    private Boolean isError;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class McpContent {
        @JsonProperty("type")
        private String type;
        
        @JsonProperty("text")
        private String text;
        
        @JsonProperty("data")
        private Object data;
    }
}
