package com.example.springvueapp.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;

/**
 * MCP Tool call request parameters
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class McpToolCallRequest {
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("arguments")
    private Map<String, Object> arguments;
}
