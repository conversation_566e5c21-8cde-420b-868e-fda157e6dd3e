package com.example.springvueapp.service;

import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.repository.McpServerConfigurationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * Service for managing MCP server configurations
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class McpConfigurationService {
    
    private final McpServerConfigurationRepository configurationRepository;
    
    /**
     * Create a new MCP server configuration
     */
    public Mono<McpServerConfiguration> createConfiguration(McpServerConfiguration config, Long userId) {
        return validateConfiguration(config)
                .then(configurationRepository.existsByNameAndUserId(config.getName(), userId))
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.error(new RuntimeException("Configuration with name '" + config.getName() + "' already exists"));
                    }
                    
                    config.setUserId(userId);
                    config.setCreatedAt(LocalDateTime.now());
                    config.setUpdatedAt(LocalDateTime.now());
                    
                    return configurationRepository.save(config);
                })
                .doOnSuccess(saved -> log.info("Created MCP configuration: {} for user: {}", saved.getName(), userId))
                .doOnError(error -> log.error("Failed to create MCP configuration", error));
    }
    
    /**
     * Update an existing MCP server configuration
     */
    public Mono<McpServerConfiguration> updateConfiguration(Long configId, McpServerConfiguration config, Long userId) {
        return configurationRepository.findById(configId)
                .switchIfEmpty(Mono.error(new RuntimeException("Configuration not found")))
                .flatMap(existing -> {
                    if (!existing.getUserId().equals(userId)) {
                        return Mono.error(new RuntimeException("Access denied"));
                    }
                    
                    // Update fields
                    existing.setName(config.getName());
                    existing.setDescription(config.getDescription());
                    existing.setCommand(config.getCommand());
                    existing.setArguments(config.getArguments());
                    existing.setEnvironment(config.getEnvironment());
                    existing.setWorkingDirectory(config.getWorkingDirectory());
                    existing.setDockerImage(config.getDockerImage());
                    existing.setResourceLimits(config.getResourceLimits());
                    existing.setNetworkConfig(config.getNetworkConfig());
                    existing.setVolumeMounts(config.getVolumeMounts());
                    existing.setTimeoutSeconds(config.getTimeoutSeconds());
                    existing.setAutoRestart(config.getAutoRestart());
                    existing.setEnabled(config.getEnabled());
                    existing.setUpdatedAt(LocalDateTime.now());
                    
                    return validateConfiguration(existing)
                            .then(configurationRepository.save(existing));
                })
                .doOnSuccess(updated -> log.info("Updated MCP configuration: {}", updated.getName()))
                .doOnError(error -> log.error("Failed to update MCP configuration: {}", configId, error));
    }
    
    /**
     * Delete an MCP server configuration
     */
    public Mono<Void> deleteConfiguration(Long configId, Long userId) {
        return configurationRepository.findById(configId)
                .switchIfEmpty(Mono.error(new RuntimeException("Configuration not found")))
                .flatMap(config -> {
                    if (!config.getUserId().equals(userId)) {
                        return Mono.error(new RuntimeException("Access denied"));
                    }
                    
                    return configurationRepository.delete(config);
                })
                .doOnSuccess(v -> log.info("Deleted MCP configuration: {}", configId))
                .doOnError(error -> log.error("Failed to delete MCP configuration: {}", configId, error));
    }
    
    /**
     * Get a configuration by ID
     */
    public Mono<McpServerConfiguration> getConfiguration(Long configId, Long userId) {
        return configurationRepository.findById(configId)
                .switchIfEmpty(Mono.error(new RuntimeException("Configuration not found")))
                .flatMap(config -> {
                    if (!config.getUserId().equals(userId)) {
                        return Mono.error(new RuntimeException("Access denied"));
                    }
                    return Mono.just(config);
                });
    }
    
    /**
     * Get all configurations for a user
     */
    public Flux<McpServerConfiguration> getUserConfigurations(Long userId) {
        return configurationRepository.findByUserId(userId)
                .doOnNext(config -> log.debug("Retrieved configuration: {} for user: {}", config.getName(), userId));
    }
    
    /**
     * Get enabled configurations for a user
     */
    public Flux<McpServerConfiguration> getEnabledConfigurations(Long userId) {
        return configurationRepository.findByUserIdAndEnabled(userId, true);
    }
    
    /**
     * Toggle configuration enabled status
     */
    public Mono<McpServerConfiguration> toggleEnabled(Long configId, Long userId) {
        return configurationRepository.findById(configId)
                .switchIfEmpty(Mono.error(new RuntimeException("Configuration not found")))
                .flatMap(config -> {
                    if (!config.getUserId().equals(userId)) {
                        return Mono.error(new RuntimeException("Access denied"));
                    }
                    
                    config.setEnabled(!config.getEnabled());
                    config.setUpdatedAt(LocalDateTime.now());
                    
                    return configurationRepository.save(config);
                })
                .doOnSuccess(config -> log.info("Toggled enabled status for configuration: {} to: {}", 
                        config.getName(), config.getEnabled()));
    }
    
    private Mono<Void> validateConfiguration(McpServerConfiguration config) {
        return Mono.fromRunnable(() -> {
            if (config.getName() == null || config.getName().trim().isEmpty()) {
                throw new RuntimeException("Configuration name is required");
            }
            
            if (config.getCommand() == null || config.getCommand().trim().isEmpty()) {
                throw new RuntimeException("Command is required");
            }
            
            if (config.getDockerImage() == null || config.getDockerImage().trim().isEmpty()) {
                throw new RuntimeException("Docker image is required");
            }
            
            // Additional validation logic can be added here
        });
    }
}
