package com.example.springvueapp.model;

import com.example.springvueapp.sandbox.SandboxStatus;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * Represents a running MCP server instance
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table("mcp_server_instances")
public class McpServerInstance {
    
    @Id
    private Long id;
    
    private Long configurationId;
    
    private String sandboxId; // ID of the sandbox instance
    
    private SandboxStatus status;
    
    private String sandboxType; // e.g., "docker", "kubernetes"
    
    private LocalDateTime startedAt;
    
    private LocalDateTime stoppedAt;
    
    private String errorMessage;
    
    private Long userId; // Owner of this instance
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
}
