package com.example.springvueapp.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Configuration for an MCP server instance
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table("mcp_server_configurations")
public class McpServerConfiguration {
    
    @Id
    private Long id;
    
    @NotBlank(message = "Name is required")
    private String name;
    
    private String description;
    
    @NotBlank(message = "Command is required")
    private String command;
    
    private String arguments; // JSON string of List<String>
    
    private String environment; // JSON string of Map<String, String>
    
    private String workingDirectory;
    
    @NotBlank(message = "Docker image is required")
    private String dockerImage;
    
    private String resourceLimits; // JSON string of ResourceLimits
    
    private String networkConfig; // JSON string of NetworkConfig
    
    private String volumeMounts; // JSON string of List<VolumeMount>
    
    private Integer timeoutSeconds;
    
    @Builder.Default
    private Boolean autoRestart = false;
    
    @Builder.Default
    private Boolean enabled = true;
    
    @NotNull
    private Long userId; // Owner of this configuration
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    // Helper methods for JSON serialization/deserialization
    public List<String> getArgumentsList() {
        // This would be implemented with Jackson ObjectMapper
        return null; // Placeholder
    }
    
    public void setArgumentsList(List<String> arguments) {
        // This would be implemented with Jackson ObjectMapper
    }
    
    public Map<String, String> getEnvironmentMap() {
        // This would be implemented with Jackson ObjectMapper
        return null; // Placeholder
    }
    
    public void setEnvironmentMap(Map<String, String> environment) {
        // This would be implemented with Jackson ObjectMapper
    }
}
