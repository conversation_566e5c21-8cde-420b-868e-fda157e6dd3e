package com.example.springvueapp.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * MCP 服务器实例的配置
 */
@Table("mcp_server_configurations")
public class McpServerConfiguration {

    @Id
    private Long id;

    @NotBlank(message = "Name is required")
    private String name;

    private String description;

    @NotBlank(message = "Command is required")
    private String command;

    private String arguments; // JSON string of List<String>

    private String environment; // JSON string of Map<String, String>

    private String workingDirectory;

    @NotBlank(message = "Docker image is required")
    private String dockerImage;

    private String resourceLimits; // JSON string of ResourceLimits

    private String networkConfig; // JSON string of NetworkConfig

    private String volumeMounts; // JSON string of List<VolumeMount>

    private Integer timeoutSeconds;

    private Boolean autoRestart = false;

    private Boolean enabled = true;

    @NotNull
    private Long userId; // 此配置的所有者

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public McpServerConfiguration() {
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getCommand() { return command; }
    public void setCommand(String command) { this.command = command; }

    public String getArguments() { return arguments; }
    public void setArguments(String arguments) { this.arguments = arguments; }

    public String getEnvironment() { return environment; }
    public void setEnvironment(String environment) { this.environment = environment; }

    public String getWorkingDirectory() { return workingDirectory; }
    public void setWorkingDirectory(String workingDirectory) { this.workingDirectory = workingDirectory; }

    public String getDockerImage() { return dockerImage; }
    public void setDockerImage(String dockerImage) { this.dockerImage = dockerImage; }

    public String getResourceLimits() { return resourceLimits; }
    public void setResourceLimits(String resourceLimits) { this.resourceLimits = resourceLimits; }

    public String getNetworkConfig() { return networkConfig; }
    public void setNetworkConfig(String networkConfig) { this.networkConfig = networkConfig; }

    public String getVolumeMounts() { return volumeMounts; }
    public void setVolumeMounts(String volumeMounts) { this.volumeMounts = volumeMounts; }

    public Integer getTimeoutSeconds() { return timeoutSeconds; }
    public void setTimeoutSeconds(Integer timeoutSeconds) { this.timeoutSeconds = timeoutSeconds; }

    public Boolean getAutoRestart() { return autoRestart; }
    public void setAutoRestart(Boolean autoRestart) { this.autoRestart = autoRestart; }

    public Boolean getEnabled() { return enabled; }
    public void setEnabled(Boolean enabled) { this.enabled = enabled; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
