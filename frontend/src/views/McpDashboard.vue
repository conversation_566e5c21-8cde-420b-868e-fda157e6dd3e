<template>
  <div class="mcp-dashboard">
    <div class="dashboard-header">
      <h1 class="text-3xl font-bold text-gray-900">MCP Server Dashboard</h1>
      <button 
        @click="showCreateModal = true"
        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium"
      >
        Add New Server
      </button>
    </div>

    <!-- Erro<PERSON> -->
    <div v-if="mcpStore.error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-800">{{ mcpStore.error }}</p>
        </div>
        <div class="ml-auto pl-3">
          <button @click="mcpStore.clearError()" class="text-red-400 hover:text-red-600">
            <span class="sr-only">Dismiss</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Configurations</p>
            <p class="text-2xl font-semibold text-gray-900">{{ mcpStore.configurations.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Enabled</p>
            <p class="text-2xl font-semibold text-gray-900">{{ mcpStore.enabledConfigurations.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Running Instances</p>
            <p class="text-2xl font-semibold text-gray-900">{{ mcpStore.runningInstances.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Instances</p>
            <p class="text-2xl font-semibold text-gray-900">{{ mcpStore.instances.length }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Configurations Table -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Server Configurations</h2>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Docker Image</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Instance Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="config in mcpStore.configurations" :key="config.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ config.name }}</div>
                <div class="text-sm text-gray-500">{{ config.description }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ config.dockerImage }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="config.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'" 
                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ config.enabled ? 'Enabled' : 'Disabled' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span v-if="getInstanceForConfig(config.id!)" 
                      :class="getStatusColor(getInstanceForConfig(config.id!)?.status)"
                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ getInstanceForConfig(config.id!)?.status }}
                </span>
                <span v-else class="text-sm text-gray-500">Not running</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button 
                  v-if="!getInstanceForConfig(config.id!)"
                  @click="startServer(config.id!)"
                  :disabled="!config.enabled || mcpStore.loading"
                  class="text-green-600 hover:text-green-900 disabled:text-gray-400"
                >
                  Start
                </button>
                <button 
                  v-else
                  @click="stopServer(getInstanceForConfig(config.id!)!.sandboxId)"
                  :disabled="mcpStore.loading"
                  class="text-red-600 hover:text-red-900 disabled:text-gray-400"
                >
                  Stop
                </button>
                <button 
                  @click="editConfiguration(config)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  Edit
                </button>
                <button 
                  @click="toggleConfiguration(config.id!)"
                  :disabled="mcpStore.loading"
                  class="text-yellow-600 hover:text-yellow-900 disabled:text-gray-400"
                >
                  {{ config.enabled ? 'Disable' : 'Enable' }}
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <McpConfigurationModal 
      v-if="showCreateModal || editingConfig"
      :config="editingConfig"
      @close="closeModal"
      @save="handleSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useMcpStore } from '@/store/mcp'
import type { McpServerConfiguration, McpServerInstance } from '@/types/mcp'
import McpConfigurationModal from '@/components/McpConfigurationModal.vue'

const mcpStore = useMcpStore()
const showCreateModal = ref(false)
const editingConfig = ref<McpServerConfiguration | null>(null)

onMounted(async () => {
  await Promise.all([
    mcpStore.fetchConfigurations(),
    mcpStore.fetchInstances()
  ])
})

const getInstanceForConfig = (configId: number): McpServerInstance | undefined => {
  return mcpStore.instances.find(instance => 
    instance.configurationId === configId && 
    (instance.status === 'RUNNING' || instance.status === 'STARTING')
  )
}

const getStatusColor = (status?: string) => {
  switch (status) {
    case 'RUNNING': return 'bg-green-100 text-green-800'
    case 'STARTING': return 'bg-yellow-100 text-yellow-800'
    case 'STOPPING': return 'bg-orange-100 text-orange-800'
    case 'FAILED': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const startServer = async (configId: number) => {
  try {
    await mcpStore.startServer(configId)
  } catch (error) {
    console.error('Failed to start server:', error)
  }
}

const stopServer = async (sandboxId: string) => {
  try {
    await mcpStore.stopServer(sandboxId)
  } catch (error) {
    console.error('Failed to stop server:', error)
  }
}

const editConfiguration = (config: McpServerConfiguration) => {
  editingConfig.value = { ...config }
}

const toggleConfiguration = async (configId: number) => {
  try {
    await mcpStore.toggleConfiguration(configId)
  } catch (error) {
    console.error('Failed to toggle configuration:', error)
  }
}

const closeModal = () => {
  showCreateModal.value = false
  editingConfig.value = null
}

const handleSave = async (config: McpServerConfiguration) => {
  try {
    if (editingConfig.value) {
      await mcpStore.updateConfiguration(editingConfig.value.id!, config)
    } else {
      await mcpStore.createConfiguration(config)
    }
    closeModal()
  } catch (error) {
    console.error('Failed to save configuration:', error)
  }
}
</script>

<style scoped>
.mcp-dashboard {
  @apply p-6 max-w-7xl mx-auto;
}

.dashboard-header {
  @apply flex justify-between items-center mb-8;
}
</style>
