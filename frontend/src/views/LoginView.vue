<template>
  <div class="flex justify-center items-center min-h-screen p-4 bg-gray-100">
    <div class="bg-white rounded-lg shadow-md p-8 w-full max-w-md">
      <h1 class="text-2xl font-bold text-center text-primary mb-6">
        {{ isLogin ? '登录' : '注册' }}
      </h1>

      <div v-if="authStore.error" class="error-message">
        {{ authStore.error }}
      </div>

      <form @submit.prevent="submitForm" class="space-y-4">
        <div>
          <label for="username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
          <input
            type="text"
            id="username"
            v-model="form.username"
            class="form-control"
            required
          />
        </div>

        <div v-if="!isLogin">
          <label for="email" class="block text-sm font-medium text-gray-700 mb-1">电子邮箱</label>
          <input
            type="email"
            id="email"
            v-model="form.email"
            class="form-control"
            required
          />
        </div>

        <div>
          <label for="password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
          <input
            type="password"
            id="password"
            v-model="form.password"
            class="form-control"
            required
          />
        </div>

        <div v-if="!isLogin">
          <label for="fullName" class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
          <input
            type="text"
            id="fullName"
            v-model="form.fullName"
            class="form-control"
            required
          />
        </div>

        <button
          type="submit"
          class="w-full btn mt-2"
          :disabled="authStore.loading"
          :class="{'opacity-70 cursor-not-allowed': authStore.loading}"
        >
          {{ isLogin ? '登录' : '注册' }}
        </button>

        <div class="text-center mt-4">
          <a
            href="#"
            @click.prevent="toggleForm"
            class="text-secondary hover:underline text-sm"
          >
            {{ isLogin ? '没有账号？点击注册' : '已有账号？点击登录' }}
          </a>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { RegisterRequest } from '@/types/user'

interface LoginForm {
  username: string;
  email: string;
  password: string;
  fullName: string;
}

const router = useRouter()
const authStore = useAuthStore()

const isLogin = ref<boolean>(true)
const form = ref<LoginForm>({
  username: '',
  email: '',
  password: '',
  fullName: ''
})

const toggleForm = (): void => {
  isLogin.value = !isLogin.value
  authStore.error = null
}

const submitForm = async (): Promise<void> => {
  let success = false

  if (isLogin.value) {
    success = await authStore.login(form.value.username, form.value.password)
  } else {
    const registerData: RegisterRequest = {
      username: form.value.username,
      email: form.value.email,
      password: form.value.password,
      fullName: form.value.fullName
    }

    success = await authStore.register(registerData)
    if (success) {
      isLogin.value = true
      form.value = {
        username: form.value.username,
        email: '',
        password: '',
        fullName: ''
      }
      return
    }
  }

  if (success) {
    router.push('/')
  }
}
</script>
