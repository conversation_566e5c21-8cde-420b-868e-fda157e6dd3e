import { defineStore } from 'pinia'
import axios from 'axios'
import type { 
  McpServerConfiguration, 
  McpServerInstance, 
  McpTool, 
  McpServerInfo,
  McpToolCallResponse,
  SandboxResourceUsage 
} from '@/types/mcp'

interface McpState {
  configurations: McpServerConfiguration[]
  instances: McpServerInstance[]
  loading: boolean
  error: string | null
}

export const useMcpStore = defineStore('mcp', {
  state: (): McpState => ({
    configurations: [],
    instances: [],
    loading: false,
    error: null
  }),

  getters: {
    enabledConfigurations: (state) => 
      state.configurations.filter(config => config.enabled),
    
    runningInstances: (state) => 
      state.instances.filter(instance => instance.status === 'RUNNING'),
    
    getConfigurationById: (state) => (id: number) =>
      state.configurations.find(config => config.id === id),
    
    getInstanceById: (state) => (id: number) =>
      state.instances.find(instance => instance.id === id)
  },

  actions: {
    // Configuration management
    async fetchConfigurations() {
      this.loading = true
      this.error = null
      try {
        const response = await axios.get('/api/mcp/configurations')
        this.configurations = response.data
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch configurations'
        throw error
      } finally {
        this.loading = false
      }
    },

    async createConfiguration(config: McpServerConfiguration) {
      this.loading = true
      this.error = null
      try {
        const response = await axios.post('/api/mcp/configurations', config)
        this.configurations.push(response.data)
        return response.data
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to create configuration'
        throw error
      } finally {
        this.loading = false
      }
    },

    async updateConfiguration(id: number, config: McpServerConfiguration) {
      this.loading = true
      this.error = null
      try {
        const response = await axios.put(`/api/mcp/configurations/${id}`, config)
        const index = this.configurations.findIndex(c => c.id === id)
        if (index !== -1) {
          this.configurations[index] = response.data
        }
        return response.data
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to update configuration'
        throw error
      } finally {
        this.loading = false
      }
    },

    async deleteConfiguration(id: number) {
      this.loading = true
      this.error = null
      try {
        await axios.delete(`/api/mcp/configurations/${id}`)
        this.configurations = this.configurations.filter(c => c.id !== id)
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to delete configuration'
        throw error
      } finally {
        this.loading = false
      }
    },

    async toggleConfiguration(id: number) {
      this.loading = true
      this.error = null
      try {
        const response = await axios.post(`/api/mcp/configurations/${id}/toggle`)
        const index = this.configurations.findIndex(c => c.id === id)
        if (index !== -1) {
          this.configurations[index] = response.data
        }
        return response.data
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to toggle configuration'
        throw error
      } finally {
        this.loading = false
      }
    },

    // Instance management
    async fetchInstances() {
      this.loading = true
      this.error = null
      try {
        const response = await axios.get('/api/mcp/proxy/instances')
        this.instances = response.data
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch instances'
        throw error
      } finally {
        this.loading = false
      }
    },

    async startServer(configId: number) {
      this.loading = true
      this.error = null
      try {
        const response = await axios.post(`/api/mcp/proxy/start/${configId}`)
        this.instances.push(response.data)
        return response.data
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to start server'
        throw error
      } finally {
        this.loading = false
      }
    },

    async stopServer(sandboxId: string) {
      this.loading = true
      this.error = null
      try {
        await axios.post(`/api/mcp/proxy/stop/${sandboxId}`)
        const index = this.instances.findIndex(i => i.sandboxId === sandboxId)
        if (index !== -1) {
          this.instances[index].status = 'STOPPED'
          this.instances[index].stoppedAt = new Date().toISOString()
        }
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to stop server'
        throw error
      } finally {
        this.loading = false
      }
    },

    // MCP operations
    async getTools(sandboxId: string): Promise<McpTool[]> {
      try {
        const response = await axios.get(`/api/mcp/proxy/${sandboxId}/tools`)
        return response.data
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to get tools'
        throw error
      }
    },

    async getServerInfo(sandboxId: string): Promise<McpServerInfo> {
      try {
        const response = await axios.get(`/api/mcp/proxy/${sandboxId}/info`)
        return response.data
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to get server info'
        throw error
      }
    },

    async callTool(sandboxId: string, toolName: string, arguments: Record<string, any>): Promise<McpToolCallResponse> {
      try {
        const response = await axios.post(`/api/mcp/proxy/${sandboxId}/tools/${toolName}`, arguments)
        return response.data
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to call tool'
        throw error
      }
    },

    async getResourceUsage(sandboxId: string): Promise<SandboxResourceUsage> {
      try {
        const response = await axios.get(`/api/mcp/proxy/instances/${sandboxId}/resources`)
        return response.data
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to get resource usage'
        throw error
      }
    },

    clearError() {
      this.error = null
    }
  }
})
